<template>
  <!-- 金属价格与利润统计页面 -->
  <div class="cw-statistics-page">
    <!-- 金属价格折线图 -->
    <a-card title="金属价格（月）" :bordered="false">
      <!-- 工具栏 -->
      <a-space style="margin-bottom: 12px;">
        <span>基准日期:</span>
        <a-date-picker v-model:value="metalDate" :allowClear="false" :disabled-date="disableMetalDate" />
        <span>金属类型:</span>
        <a-select v-model:value="selectedMetal" style="width: 120px;">
          <a-select-option v-for="m in metals" :key="m.key" :value="m.key">{{ m.name }}</a-select-option>
        </a-select>
      </a-space>

      <div ref="metalPriceRef" style="height:400px;"></div>
    </a-card>

    <!-- 利润统计 -->
    <a-card title="利润概览" :bordered="false" style="margin-top:16px;">
      <!-- 工具栏 -->
      <div class="profit-toolbar">
        <!-- 近30天切换按钮 -->
        <div class="toolbar-section">
          <a-button
            :type="isRecent30Mode ? 'primary' : 'default'"
            @click="toggleRecent30Mode"
            class="recent-toggle-btn"
          >
            <template #icon>
              <CalendarOutlined />
            </template>
            近30天
          </a-button>
        </div>

        <!-- 时间维度选择器 -->
        <div class="toolbar-section">
          <span class="toolbar-label">时间维度:</span>
          <a-radio-group
            v-model:value="dimension"
            :disabled="isRecent30Mode"
            class="dimension-selector"
            :class="{ 'disabled-group': isRecent30Mode }"
          >
            <a-radio-button value="day">日</a-radio-button>
            <a-radio-button value="month">月</a-radio-button>
            <a-radio-button value="year">年</a-radio-button>
          </a-radio-group>
        </div>

        <!-- 日期选择器 -->
        <div class="toolbar-section">
          <span class="toolbar-label">选择日期:</span>
          <a-date-picker
            v-model:value="profitDate"
            :allowClear="false"
            :picker="profitPickerType"
            :disabled-date="disableProfitDate"
            class="date-picker"
          />
        </div>
      </div>

      <div class="profit-container">
        <div class="profit-left">
          <div class="profit-card">
            <!-- 总利润 -->
            <div class="profit-main">
              <div class="value">{{ formatNumber(profitStat.profit) }}</div>
              <div class="unit">{{ profitStat.unit }}</div>
            </div>
            <div class="period">{{ profitStat.period }}</div>

            <a-divider style="margin: 12px 0" />

            <!-- 差值列表 -->
            <div class="diff-list">
              <div class="diff-item" v-for="d in diffList" :key="d.label">
                <span class="label">{{ d.label }}</span>
                <component :is="d.icon" v-if="d.icon" :class="d.class" />
                <span :class="d.class">{{ d.text }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="profit-right">
          <div ref="profitTrendRef" style="height:300px;"></div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-statistics">
  import { ref, onMounted, watch, computed, Ref } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口方法
  import { metalPriceMonth, profitStatistics, profitTrend } from '/@/api/cw/statistics';
  // 工具函数
  import { formatNumber } from '/@/utils/showUtils';
  // ECharts 封装
  import { useECharts } from '/@/hooks/web/useECharts';
  // Icons
  import { ArrowUpOutlined, ArrowDownOutlined, CalendarOutlined } from '@ant-design/icons-vue';

  /** 差值计算列表 */
  const diffList = computed(() => {
    return [
      { label: '环比', value: profitStat.value?.hb },
      { label: '同比', value: profitStat.value?.tb },
      { label: '计划比', value: profitStat.value?.jhb },
    ].map((d) => {
      const num = d.value ?? null;
      const up = num !== null && num > 0;
      const down = num !== null && num < 0;
      return {
        label: d.label,
        text: formatDiff(num as any),
        icon: up ? ArrowUpOutlined : down ? ArrowDownOutlined : null,
        class: up ? 'up' : down ? 'down' : '',
      };
    });
  });

  /** 图表 DOM 引用 */
  const metalPriceRef = ref<HTMLDivElement | null>(null);
  const profitTrendRef = ref<HTMLDivElement | null>(null);

  /** ECharts 实例方法 */
  const { setOptions: setMetalPriceOptions } = useECharts(metalPriceRef as unknown as Ref<HTMLDivElement>);
  const { setOptions: setProfitTrendOptions } = useECharts(profitTrendRef as unknown as Ref<HTMLDivElement>);

  /** 数据 */
  const metalPriceList = ref<any[]>([]);
  const profitStat = ref<any>({});
  const profitTrendList = ref<any[]>([]);

  /* 选择器相关 */
  const metalDate = ref(dayjs().subtract(2, 'day'));
  const dimension = ref<'day' | 'month' | 'year' | 'recent'>('day');
  const profitDate = ref(dayjs().subtract(2, 'day'));

  // 近30天模式状态
  const STORAGE_KEY = 'cw-statistics-recent30';
  const isRecent30Mode = ref<boolean>((localStorage.getItem(STORAGE_KEY) === 'true') || false);

  // 计算属性：兼容原有的 rangeMode 逻辑
  const rangeMode = computed(() => isRecent30Mode.value ? 'recent30' : 'normal');

  /** 金属配置 */
  const metals = [
    { key: 'cu', name: '铜价' },
    { key: 'au', name: '金价' },
    { key: 'ag', name: '银价' },
  ] as const;
  const selectedMetal = ref(metals[0].key); // 默认选中第一个金属

  // 根据维度返回 date-picker 类型
  const profitPickerType = computed(() => {
    // 近30天只按日选择结束日
    if (isRecent30Mode.value) return 'date';
    return dimension.value === 'day' ? 'date' : (dimension.value as any);
  });

  // 禁用利润选择器的不可选日期（最多到今天-2天，但月份选择允许当前月份）
  function disableProfitDate(current: any) {
    const max = dayjs().subtract(2, 'day').endOf('day');
    if (!current) return false;

    // 月份选择时，允许选择当前月份（只要当前月份有至少3天的数据）
    if (profitPickerType.value === 'month') {
      const currentMonth = dayjs(current);
      const now = dayjs();
      // 如果是当前月份，检查是否已经过了至少3天
      if (currentMonth.isSame(now, 'month') && currentMonth.isSame(now, 'year')) {
        return now.date() < 3; // 当前月份少于3天时禁用
      }
      // 其他月份按原逻辑：月末不能超过max
      return currentMonth.endOf('month').isAfter(max);
    }

    // 年份选择时的逻辑保持不变
    if (profitPickerType.value === 'year') {
      return dayjs(current).endOf('year').isAfter(max);
    }

    // 日期选择时的逻辑保持不变
    return dayjs(current).isAfter(max);
  }

  // 禁用金属价格日期（最多到今天-2天）
  function disableMetalDate(current: any) {
    const max = dayjs().subtract(2, 'day').endOf('day');
    if (!current) return false;
    return dayjs(current).isAfter(max);
  }

  // 监听维度或日期变化自动刷新
  watch([dimension, profitDate, isRecent30Mode], () => {
    fetchProfit();
  });

  watch(metalDate, () => {
    fetchMetalPrice();
  });

  /** 监听变化刷新金属图表 */
  watch(selectedMetal, () => {
    updateMetalPriceChart();
  });

  /** 页面初始化加载数据 */
  onMounted(() => {
    // 根据近30天模式初始化维度
    if (isRecent30Mode.value) {
      dimension.value = 'recent';
    } else if (dimension.value === 'recent') {
      dimension.value = 'day';
    }
    fetchMetalPrice();
    fetchProfit();
  });

  /** 切换近30天模式 */
  function toggleRecent30Mode() {
    isRecent30Mode.value = !isRecent30Mode.value;
    localStorage.setItem(STORAGE_KEY, isRecent30Mode.value.toString());

    // 根据模式调整维度
    if (isRecent30Mode.value) {
      dimension.value = 'recent';
    } else if (dimension.value === 'recent') {
      dimension.value = 'day';
    }
    // API调用由watch监听器自动处理
  }

  /** 查询金属价格 */
  async function fetchMetalPrice() {
    try {
      const params: any = { date: metalDate.value.format('YYYY-MM-DD') };
      if (isRecent30Mode.value) params.recentDays = 30;
      metalPriceList.value = await metalPriceMonth(params);
      updateMetalPriceChart();
    } catch (e) {
      console.error(e);
      message.error('金属价格获取失败');
    }
  }

  /** 查询利润数据 */
  async function fetchProfit() {
    try {
      const dateStr = profitDate.value.format('YYYY-MM-DD');
      const isRecent = isRecent30Mode.value || dimension.value === 'recent';
      const dim = isRecent ? 'recent' : (dimension.value as any);
      const baseParams: any = { date: dateStr, dimension: dim };
      if (isRecent) baseParams.recentDays = 30;
      profitStat.value = await profitStatistics(baseParams);
      profitTrendList.value = await profitTrend(baseParams);
      updateProfitTrendChart();
    } catch (e) {
      console.error(e);
      message.error('利润数据获取失败');
    }
  }

  /** 更新金属价格折线图 */
  function updateMetalPriceChart() {
    if (!metalPriceList.value?.length) return;
    const metal = metals.find((m) => m.key === selectedMetal.value);

    // 过滤掉为 0 或 null 的数据点
    const categories: string[] = [];
    const dataSeries: number[] = [];
    metalPriceList.value.forEach((item) => {
      const val = item[selectedMetal.value];
      if (val && val !== 0) {
        categories.push(item.date?.slice(5));
        dataSeries.push(val);
      }
    });

    if (!dataSeries.length) {
      // 没有有效数据
      setMetalPriceOptions({});
      return;
    }

    // 根据数据动态计算 y 轴范围，增加 10% padding
    const minVal = Math.min(...dataSeries);
    const maxVal = Math.max(...dataSeries);
    const padding = (maxVal - minVal) * 0.1 || 1;

    setMetalPriceOptions({
      title: { text: metal?.name, left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: categories },
      yAxis: {
        type: 'value',
        min: Math.floor((minVal - padding) * 100) / 100, // 保留 2 位小数
        max: Math.ceil((maxVal + padding) * 100) / 100,
      },
      series: [
        {
          name: metal?.name || '',
          type: 'line',
          smooth: true,
          data: dataSeries,
        },
      ],
    });
  }

  /** 更新利润趋势折线图 */
  function updateProfitTrendChart() {
    if (!profitTrendList.value?.length) return;
    const categories = profitTrendList.value.map((d) => d.period);
    const data = profitTrendList.value.map((d) => d.profit ?? 0);
    setProfitTrendOptions({
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: categories },
      yAxis: { type: 'value' },
      series: [
        {
          name: '利润',
          type: 'line',
          smooth: true,
          data,
        },
      ],
    });
  }

  /** 差值格式化（带符号） */
  function formatDiff(value: number) {
    if (value === null || value === undefined) return '--';
    const num = Number(value);
    const sign = num > 0 ? '+' : '';
    return sign + formatNumber(num);
  }

  // no-op
</script>

<style scoped>
  .cw-statistics-page {
    padding: 16px;
  }

  .profit-container {
    display: flex;
    align-items: center; /* 垂直居中 */
  }

  .profit-left {
    width: 320px; /* Increased width for larger card */
  }

  .profit-right {
    flex: 1;
    margin-left: 16px;
  }

  .profit-card {
    background: linear-gradient(135deg, #f0f5ff 0%, #d6e4ff 100%);
    padding: 24px; /* Provide more spacing to make the card appear larger */
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    text-align: center;
  }

  .profit-main {
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }

  .profit-main .value {
    font-size: 32px; /* Slightly larger font for emphasis */
    font-weight: 600;
    margin-right: 4px;
  }

  .profit-main .unit {
    font-size: 14px;
    color: #595959;
  }

  .profit-card .period {
    font-size: 14px;
    color: #8c8c8c;
    margin-top: 4px;
  }

  .diff-list {
    display: flex;
    justify-content: space-around;
    margin-top: 8px;
  }

  /* 仅让标签不换行，避免竖排；其它内容可自动换行 */
  .diff-item .label {
    white-space: nowrap;
    margin-right: 4px;
  }

  .up {
    color: #52c41a;
  }

  .down {
    color: #ff4d4f;
  }

  /* 新增工具栏样式 */
  .profit-toolbar {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }

  .toolbar-section {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .toolbar-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    white-space: nowrap;
  }

  .recent-toggle-btn {
    height: 32px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .recent-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .dimension-selector {
    transition: all 0.3s ease;
  }

  .dimension-selector.disabled-group {
    opacity: 0.5;
    pointer-events: none;
  }

  .dimension-selector .ant-radio-button-wrapper {
    height: 32px;
    line-height: 30px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .dimension-selector .ant-radio-button-wrapper:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .dimension-selector .ant-radio-button-wrapper:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .date-picker {
    width: 140px;
    height: 32px;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .date-picker:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .profit-toolbar {
      flex-wrap: wrap;
      gap: 16px;
    }

    .toolbar-section {
      min-width: fit-content;
    }
  }

  @media (max-width: 768px) {
    .profit-toolbar {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .toolbar-section {
      justify-content: space-between;
    }

    .date-picker {
      width: 100%;
    }
  }
</style>
